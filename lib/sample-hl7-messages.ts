export interface SampleHL7Message {
  id: string
  name: string
  description: string
  messageType: string
  category: string
  rawMessage: string
  tags: string[]
}

export const SAMPLE_HL7_MESSAGES: SampleHL7Message[] = [
  {
    id: "adt-a01-admission",
    name: "Patient Admission (ADT^A01)",
    description: "Standard patient admission message with complete demographics",
    messageType: "ADT^A01",
    category: "Admission/Discharge/Transfer",
    tags: ["admission", "demographics", "basic"],
    rawMessage: `MSH|^~\\&|SENDING_APP|SENDING_FACILITY|RECEIVING_APP|RECEIVING_FACILITY|20240710120000||ADT^A01|MSG001|P|2.5
EVN||20240710120000|||^SMITH^JOHN^J^DR^MD
PID|1||PATID1234^5^M11^ADT1^MR^UNIVERSITY_HOSPITAL~123456789^^^USA^SS||DOE^JOHN^ALEXANDER|19800101|M||C|1200 N ELM STREET^^GREENSBORO^NC^27401-1020|GL|(919)379-1212|(919)271-3434~(919)277-3114||S||PATID12345001^2^M10^ADT1^AN^A|123456789|9-87654^NC
NK1|1|DOE^JANE^MARIE|SPO|1200 N ELM STREET^^GREENSBORO^NC^27401-1020|(919)379-1212|(919)271-3434|EC
PV1|1|I|2000^2012^01||||004777^ATTEND^AARON^A|||SUR|||A|||004777^ATTEND^AARON^A|INP|CAT|||||||||||||||||||||||||20240710120000
AL1|1||^PENICILLIN^L|MO|SHORTNESS OF BREATH
DG1|1|I9|786.05^SHORTNESS OF BREATH^I9|SHORTNESS OF BREATH||A`
  },
  {
    id: "adt-a04-registration",
    name: "Patient Registration (ADT^A04)",
    description: "Patient registration without admission to inpatient care",
    messageType: "ADT^A04",
    category: "Admission/Discharge/Transfer",
    tags: ["registration", "outpatient", "basic"],
    rawMessage: `MSH|^~\\&|REGISTRATION|MAIN_HOSPITAL|PATIENT_ADMIN|MAIN_HOSPITAL|20240710130000||ADT^A04|MSG002|P|2.5
EVN||20240710130000|||^JONES^MARY^L^DR^MD
PID|1||PATID5678^5^M11^ADT1^MR^MAIN_HOSPITAL~987654321^^^USA^SS||SMITH^MARY^ELIZABETH|19750315|F||AA|456 OAK AVENUE^^CHARLOTTE^NC^28202-3456|GL|(704)555-1234|(704)555-5678||M||PATID56789002^2^M10^ADT1^AN^A|987654321|8-76543^NC
NK1|1|SMITH^ROBERT^JAMES|SPO|456 OAK AVENUE^^CHARLOTTE^NC^28202-3456|(704)555-1234|(704)555-5678|EC
PV1|1|O|CLINIC^101^01||||005888^PROVIDER^BETTY^B|||FAM|||A|||005888^PROVIDER^BETTY^B|OUT|CAT|||||||||||||||||||||||||20240710130000`
  },
  {
    id: "adt-a08-update",
    name: "Patient Information Update (ADT^A08)",
    description: "Update to patient demographic information",
    messageType: "ADT^A08",
    category: "Admission/Discharge/Transfer",
    tags: ["update", "demographics", "basic"],
    rawMessage: `MSH|^~\\&|PATIENT_ADMIN|MAIN_HOSPITAL|REGISTRATION|MAIN_HOSPITAL|20240710140000||ADT^A08|MSG003|P|2.5
EVN||20240710140000|||^ADMIN^SYSTEM^S^SYS^SYS
PID|1||PATID9999^5^M11^ADT1^MR^MAIN_HOSPITAL~111222333^^^USA^SS||JOHNSON^ROBERT^WILLIAM|19601225|M||W|789 PINE STREET^^RALEIGH^NC^27603-7890|GL|(919)555-9876|(919)555-4321||M||PATID99999003^2^M10^ADT1^AN^A|111222333|7-65432^NC
NK1|1|JOHNSON^SUSAN^ANN|SPO|789 PINE STREET^^RALEIGH^NC^27603-7890|(919)555-9876|(919)555-4321|EC
PV1|1|O|CLINIC^102^01||||006999^DOCTOR^CHARLES^C|||INT|||A|||006999^DOCTOR^CHARLES^C|OUT|CAT|||||||||||||||||||||||||20240710140000`
  },
  {
    id: "orm-o01-lab-order",
    name: "Laboratory Order (ORM^O01)",
    description: "Laboratory test order with multiple tests",
    messageType: "ORM^O01",
    category: "Orders",
    tags: ["laboratory", "orders", "clinical"],
    rawMessage: `MSH|^~\\&|LAB_SYSTEM|MAIN_HOSPITAL|LAB|MAIN_HOSPITAL|20240710150000||ORM^O01|MSG004|P|2.5
PID|1||PATID1111^5^M11^ADT1^MR^MAIN_HOSPITAL~444555666^^^USA^SS||WILLIAMS^SARAH^JANE|19850420|F||B|321 MAPLE DRIVE^^DURHAM^NC^27701-1234|GL|(919)555-2468|(919)555-1357||S||PATID11111004^2^M10^ADT1^AN^A|444555666|6-54321^NC
PV1|1|I|3000^3015^01||||007111^PHYSICIAN^DAVID^D|||MED|||A|||007111^PHYSICIAN^DAVID^D|INP|CAT|||||||||||||||||||||||||20240710150000
ORC|NW|LAB12345|LAB67890|GRP001|||^^^^^R||20240710150000|||007111^PHYSICIAN^DAVID^D
OBR|1|LAB12345|LAB67890|CBC^COMPLETE BLOOD COUNT^L|||20240710150000|||||||||007111^PHYSICIAN^DAVID^D||||||20240710180000|||F
OBR|2|LAB12346|LAB67891|BMP^BASIC METABOLIC PANEL^L|||20240710150000|||||||||007111^PHYSICIAN^DAVID^D||||||20240710180000|||F
OBR|3|LAB12347|LAB67892|LIPID^LIPID PANEL^L|||20240710150000|||||||||007111^PHYSICIAN^DAVID^D||||||20240710180000|||F`
  },
  {
    id: "oru-r01-lab-result",
    name: "Laboratory Results (ORU^R01)",
    description: "Laboratory test results with multiple observations",
    messageType: "ORU^R01",
    category: "Results",
    tags: ["laboratory", "results", "clinical"],
    rawMessage: `MSH|^~\\&|LAB|MAIN_HOSPITAL|LAB_SYSTEM|MAIN_HOSPITAL|20240710180000||ORU^R01|MSG005|P|2.5
PID|1||PATID1111^5^M11^ADT1^MR^MAIN_HOSPITAL~444555666^^^USA^SS||WILLIAMS^SARAH^JANE|19850420|F||B|321 MAPLE DRIVE^^DURHAM^NC^27701-1234|GL|(919)555-2468|(919)555-1357||S||PATID11111004^2^M10^ADT1^AN^A|444555666|6-54321^NC
OBR|1|LAB12345|LAB67890|CBC^COMPLETE BLOOD COUNT^L|||20240710150000|||||||||007111^PHYSICIAN^DAVID^D||||||20240710180000|||F
OBX|1|NM|WBC^WHITE BLOOD CELLS^L|1|7.2|10*3/uL|4.0-11.0|N|||F
OBX|2|NM|RBC^RED BLOOD CELLS^L|1|4.5|10*6/uL|4.2-5.4|N|||F
OBX|3|NM|HGB^HEMOGLOBIN^L|1|14.2|g/dL|12.0-16.0|N|||F
OBX|4|NM|HCT^HEMATOCRIT^L|1|42.1|%|36.0-46.0|N|||F
OBR|2|LAB12346|LAB67891|BMP^BASIC METABOLIC PANEL^L|||20240710150000|||||||||007111^PHYSICIAN^DAVID^D||||||20240710180000|||F
OBX|5|NM|GLU^GLUCOSE^L|1|95|mg/dL|70-100|N|||F
OBX|6|NM|BUN^BLOOD UREA NITROGEN^L|1|18|mg/dL|7-20|N|||F
OBX|7|NM|CREAT^CREATININE^L|1|1.0|mg/dL|0.6-1.2|N|||F
OBX|8|NM|NA^SODIUM^L|1|140|mmol/L|136-145|N|||F
OBX|9|NM|K^POTASSIUM^L|1|4.2|mmol/L|3.5-5.1|N|||F
OBX|10|NM|CL^CHLORIDE^L|1|102|mmol/L|98-107|N|||F
OBX|11|NM|CO2^CARBON DIOXIDE^L|1|24|mmol/L|22-28|N|||F`
  },
  {
    id: "mdm-t02-document",
    name: "Medical Document (MDM^T02)",
    description: "Medical document management message with transcription",
    messageType: "MDM^T02",
    category: "Documents",
    tags: ["document", "transcription", "clinical"],
    rawMessage: `MSH|^~\\&|DOC_SYSTEM|MAIN_HOSPITAL|EMR|MAIN_HOSPITAL|20240710160000||MDM^T02|MSG006|P|2.5
EVN||20240710160000
PID|1||PATID2222^5^M11^ADT1^MR^MAIN_HOSPITAL~777888999^^^USA^SS||BROWN^MICHAEL^THOMAS|19701010|M||W|654 CEDAR LANE^^WINSTON_SALEM^NC^27104-5678|GL|(336)555-7890|(336)555-0123||M||PATID22222005^2^M10^ADT1^AN^A|777888999|5-43210^NC
PV1|1|I|4000^4020^01||||008222^CARDIOLOGIST^EMILY^E|||CAR|||A|||008222^CARDIOLOGIST^EMILY^E|INP|CAT|||||||||||||||||||||||||20240710160000
TXA|1|CN|TX|20240710160000||||||||008222^CARDIOLOGIST^EMILY^E|008222^CARDIOLOGIST^EMILY^E||||AV|AV
OBX|1|TX|NOTE^CLINICAL NOTE^L|1|PATIENT PRESENTS WITH CHEST PAIN. EXAMINATION REVEALS NORMAL HEART SOUNDS. EKG SHOWS NORMAL SINUS RHYTHM. RECOMMEND STRESS TEST.||||F`
  }
]

export const SAMPLE_MESSAGE_CATEGORIES = [
  "Admission/Discharge/Transfer",
  "Orders", 
  "Results",
  "Documents",
  "Scheduling",
  "Financial"
]

export const SAMPLE_MESSAGE_TAGS = [
  "admission",
  "discharge", 
  "registration",
  "update",
  "demographics",
  "laboratory",
  "radiology",
  "orders",
  "results",
  "clinical",
  "document",
  "transcription",
  "basic",
  "advanced",
  "outpatient",
  "inpatient"
]
