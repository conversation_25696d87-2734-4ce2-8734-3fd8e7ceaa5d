{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:all": "npm run test && npm run test:e2e", "playwright:install": "playwright install"}, "dependencies": {"@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "1.2.1", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.1", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.2", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "1.1.4", "@types/react-syntax-highlighter": "^15.5.13", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "json-2-csv": "^5.5.9", "lucide-react": "0.468.0", "mongodb": "^6.17.0", "next": "15.1.6", "next-themes": "0.4.4", "react": "18.3.1", "react-day-picker": "^9.8.0", "react-dom": "18.3.1", "react-hook-form": "7.54.2", "react-resizable-panels": "2.1.7", "react-syntax-highlighter": "15.6.1", "recharts": "^3.1.0", "sonner": "1.7.3", "tailwind-merge": "2.5.5", "tailwindcss-animate": "1.0.7", "vaul": "^1.1.2", "xml-js": "1.6.11", "zod": "3.24.1"}, "devDependencies": {"@playwright/test": "^1.54.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "22.0.0", "@types/react": "18.3.0", "@types/react-dom": "18.3.0", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "msw": "^2.10.3", "node-mocks-http": "^1.17.2", "postcss": "8.5.0", "tailwindcss": "3.4.17", "typescript": "5.6.3"}}